import { Container, Heading } from "@medusajs/ui"
import { keepPreviousData } from "@tanstack/react-query"
import { useTranslation } from "react-i18next"
import { useMemo } from "react"

import { _DataTable } from "../../../../components/table/data-table/data-table"
import { useQuotes } from "../../../../hooks/api/quotes"
import { useDataTable } from "../../../../hooks/use-data-table"
import { useQuotesTableColumns } from "./table/columns"
import { useQuotesTableFilters } from "./table/filters"
import { useQuotesTableQuery } from "./table/query"

const PAGE_SIZE = 20

export const QuotesTable = () => {
  const { t } = useTranslation()
  const { searchParams, raw } = useQuotesTableQuery({
    pageSize: PAGE_SIZE,
  })

  // 从searchParams中提取搜索关键词
  const searchQuery = searchParams.q

  const { quotes, count, isError, error, isLoading } = useQuotes(
    {
      // 移除搜索参数，因为我们要在客户端实现搜索
      ...searchParams,
      q: undefined,
    },
    {
      placeholderData: keepPreviousData,
    }
  )

  // 客户端筛选：根据搜索关键词筛选报价
  const filteredQuotes = useMemo(() => {
    let result = quotes ?? []

    // 根据搜索关键词筛选
    if (searchQuery) {
      result = result.filter((quote) => {
        const searchTerm = searchQuery.toLowerCase()
        return (
          // 报价ID
          quote.id?.toLowerCase().includes(searchTerm) ||
          // 报价显示ID (如 #123)
          quote.draft_order?.display_id?.toString().includes(searchTerm) ||
          // 客户邮箱
          quote.customer?.email?.toLowerCase().includes(searchTerm) ||
          // 公司名称
          quote.customer?.company_name?.toLowerCase().includes(searchTerm) ||
          // 报价状态
          quote.status?.toLowerCase().includes(searchTerm)
        )
      })
    }

    return result
  }, [quotes, searchQuery])

  // 计算筛选后的数量
  const filteredCount = useMemo(() => {
    return filteredQuotes.length
  }, [filteredQuotes.length])

  const filters = useQuotesTableFilters()
  const columns = useQuotesTableColumns()

  const { table } = useDataTable({
    data: filteredQuotes ?? [],
    columns,
    enablePagination: true,
    count: filteredCount,
    pageSize: PAGE_SIZE,
  })

  if (isError) {
    throw error
  }

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading>{t("quotes.title", "Quotes")}</Heading>
      </div>
      <_DataTable
        columns={columns}
        table={table}
        pagination
        navigateTo={(row) => `/quotes/${row.original.id}`}
        filters={filters}
        count={filteredCount}
        search
        isLoading={isLoading}
        pageSize={PAGE_SIZE}
        orderBy={[
          { key: "id", label: t("quotes.table.id", "Quote ID") },
          { key: "status", label: t("quotes.table.status", "Status") },
          { key: "created_at", label: t("fields.createdAt", "Created At") },
          { key: "updated_at", label: t("fields.updatedAt", "Updated At") },
        ]}
        queryObject={raw}
        noRecords={{
          message: t("quotes.noQuotes", "No quotes found"),
        }}
      />
    </Container>
  )
}

