import { EllipsisHorizontal, PencilSquare, Trash } from "@medusajs/icons"
import { DropdownMenu, IconButton } from "@medusajs/ui"
import { Company, QueryEmployee } from "../../../../../types"

interface EmployeesActionsMenuProps {
  company: Company
  employee: QueryEmployee
}

export const EmployeesActionsMenu = ({ company, employee }: EmployeesActionsMenuProps) => {
  return (
    <DropdownMenu>
      <DropdownMenu.Trigger asChild>
        <IconButton size="small" variant="transparent">
          <EllipsisHorizontal />
        </IconButton>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content>
        <DropdownMenu.Item className="gap-x-2">
          <PencilSquare className="text-ui-fg-subtle" />
          编辑
        </DropdownMenu.Item>
        <DropdownMenu.Separator />
        <DropdownMenu.Item className="gap-x-2">
          <Trash className="text-ui-fg-subtle" />
          删除
        </DropdownMenu.Item>
      </DropdownMenu.Content>
    </DropdownMenu>
  )
}
