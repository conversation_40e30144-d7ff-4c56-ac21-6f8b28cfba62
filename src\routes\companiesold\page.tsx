import { defineRouteConfig } from "@medusajs/admin-sdk";
import { BuildingStorefront } from "@medusajs/icons";
import {
  Avatar,
  Badge,
  Container,
  Heading,
  Table,
  Text,
  Toaster,
} from "@medusajs/ui";
import { useNavigate } from "react-router-dom";
import { QueryCompany } from "../../../types";
import { useAdminCustomerGroups, useCompanies } from "../../hooks/api";
import { useB2BTranslation, usePageTitleTranslation } from "../../hooks/use-b2b-translation";
import { useMenuLabelUpdater } from "../../lib/menu-label-updater";
import { CompanyActionsMenu, CompanyCreateDrawer } from "./components";
import "../../lib/init-i18n";

const Companies = () => {
  const { t } = useB2BTranslation();
  const { t: tTitle } = usePageTitleTranslation(); // 专门用于页面标题，避免显示翻译键
  useMenuLabelUpdater(); // 启用菜单标签更新
  const navigate = useNavigate();
  const { data, isPending, isError } = useCompanies({
    fields:
      "*employees,*employees.customer,*employees.company,*customer_group,*approval_settings",
  });

  const { data: customerGroups } = useAdminCustomerGroups();

  // 使用专门的页面标题翻译hook，在任何语言下都不会显示翻译键
  const pageTitle = tTitle("routes.companies.title");

  // 显示加载状态
  if (isPending) {
    return (
      <Container className="flex flex-col p-0 overflow-hidden">
        <div className="p-6 flex justify-between">
          <Heading className="font-sans font-medium h1-core">{pageTitle}</Heading>
          <CompanyCreateDrawer />
        </div>
        <div className="flex items-center justify-center p-8">
          <Text>{t("common.loading")}</Text>
        </div>
      </Container>
    );
  }

  // 显示错误状态
  if (isError) {
    return (
      <Container className="flex flex-col p-0 overflow-hidden">
        <div className="p-6 flex justify-between">
          <Heading className="font-sans font-medium h1-core">{pageTitle}</Heading>
          <CompanyCreateDrawer />
        </div>
        <div className="flex items-center justify-center p-8">
          <Text>{t("common.error")}</Text>
        </div>
      </Container>
    );
  }

  const companies = data?.companies || [];
  const hasCompanies = companies.length > 0;

  return (
    <>
      <Container className="flex flex-col p-0 overflow-hidden">
        <div className="p-6 flex justify-between">
          <Heading className="font-sans font-medium h1-core">{pageTitle}</Heading>
          <CompanyCreateDrawer />
        </div>
        
        {hasCompanies ? (
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell></Table.HeaderCell>
                <Table.HeaderCell>{t("routes.companies.table.name")}</Table.HeaderCell>
                <Table.HeaderCell>{t("routes.companies.table.phone")}</Table.HeaderCell>
                <Table.HeaderCell>{t("routes.companies.table.email")}</Table.HeaderCell>
                <Table.HeaderCell>{t("routes.companies.table.address")}</Table.HeaderCell>
                <Table.HeaderCell>{t("routes.companies.table.employees")}</Table.HeaderCell>
                <Table.HeaderCell>{t("routes.companies.table.customerGroup")}</Table.HeaderCell>
                <Table.HeaderCell>{t("routes.companies.table.actions")}</Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {companies.map((company: QueryCompany) => (
                <Table.Row
                  key={company.id}
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() =>
                    navigate(`/companies/${company.id}`)
                  }
                >
                  <Table.Cell className="w-6 h-6 items-center justify-center">
                    <Avatar
                      src={company.logo_url || undefined}
                      fallback={company.name.charAt(0)}
                    />
                  </Table.Cell>
                  <Table.Cell>{company.name}</Table.Cell>
                  <Table.Cell>{company.phone}</Table.Cell>
                  <Table.Cell>{company.email}</Table.Cell>
                  <Table.Cell>{`${company.address}, ${company.city}, ${company.state} ${company.zip}`}</Table.Cell>
                  <Table.Cell>{company.employees?.length || 0}</Table.Cell>
                  <Table.Cell>
                    {company.customer_group?.name ? (
                      <Badge size="small" color="blue">
                        {company.customer_group.name}
                      </Badge>
                    ) : (
                      "-"
                    )}
                  </Table.Cell>
                  <Table.Cell onClick={(e) => e.stopPropagation()}>
                    <CompanyActionsMenu
                      company={company}
                      customerGroups={customerGroups}
                    />
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        ) : (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <Text className="text-ui-fg-subtle mb-2">
              {t("routes.companies.noCompanies")}
            </Text>
            <Text className="text-ui-fg-muted text-sm">
              {t("routes.companies.noCompaniesDescription")}
            </Text>
          </div>
        )}
      </Container>
      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Companies",
  icon: BuildingStorefront,
});

export default Companies;
