import {
  Button,
  CurrencyInput,
  Drawer,
  Input,
  Label,
  Switch,
  Text,
  Tooltip,
  Container,
} from "@medusajs/ui"
import { InformationCircleSolid } from "@medusajs/icons"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { Company, Employee, AdminUpdateEmployee } from "../../../../../types"
import { getCurrencySymbol } from "../../../../../lib/data/currencies"

// CoolSwitch component - matches old version styling
interface CoolSwitchProps {
  fieldName: string
  label: string
  description: string
  checked: boolean
  onChange: (checked: boolean) => void
  tooltip?: string
}

const CoolSwitch = ({ fieldName, label, description, checked, onChange, tooltip }: CoolSwitchProps) => {
  return (
    <Container className="bg-ui-bg-subtle flex flex-col gap-2">
      <div className="flex items-center gap-2">
        <Switch name={fieldName} checked={checked} onCheckedChange={onChange} />
        <Label size="xsmall" className="txt-compact-small font-medium">
          {label}
        </Label>
        {tooltip && (
          <Tooltip content={tooltip} className="z-50">
            <InformationCircleSolid color="gray" />
          </Tooltip>
        )}
      </div>
      <Text size="xsmall">{description}</Text>
    </Container>
  )
}

interface EmployeesUpdateFormProps {
  company: Company
  employee: Employee
  handleSubmit: (data: AdminUpdateEmployee) => Promise<void>
  loading: boolean
}

export const EmployeesUpdateForm = ({
  company,
  employee,
  handleSubmit,
  loading,
}: EmployeesUpdateFormProps) => {
  const { t } = useTranslation()
  const [formData, setFormData] = useState<{
    is_admin: boolean
    spending_limit: string
  }>({
    is_admin: employee.is_admin || false,
    spending_limit: employee.spending_limit?.toString() || "0",
  })

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const data: AdminUpdateEmployee = {
      is_admin: formData.is_admin,
      spending_limit: parseInt(formData.spending_limit) || 0,
    }

    await handleSubmit(data)
  }

  return (
    <form onSubmit={onSubmit}>
      <Drawer.Body className="flex flex-col gap-4">
        {/* Employee Info Display */}
        <div className="flex flex-col gap-2">
          <Label size="xsmall" className="txt-compact-small font-medium">
            员工信息
          </Label>
          <div className="bg-ui-bg-subtle p-3 rounded-lg">
            <Text size="small" className="font-medium">
              {employee.customer.first_name} {employee.customer.last_name}
            </Text>
            <Text size="xsmall" className="text-ui-fg-muted">
              {employee.customer.email}
            </Text>
            {employee.customer.phone && (
              <Text size="xsmall" className="text-ui-fg-muted">
                {employee.customer.phone}
              </Text>
            )}
          </div>
        </div>

        {/* Spending Limit */}
        <div className="flex flex-col gap-2">
          <Label size="xsmall" className="txt-compact-small font-medium">
            消费限额 ({company.currency_code?.toUpperCase() || "USD"})
          </Label>
          <CurrencyInput
            symbol={getCurrencySymbol(company.currency_code)}
            code={company.currency_code || "USD"}
            type="text"
            name="spending_limit"
            value={formData.spending_limit}
            onChange={(e) =>
              setFormData({
                ...formData,
                spending_limit: e.target.value.replace(/[^0-9]/g, ""),
              })
            }
            placeholder="输入消费限额"
          />
        </div>

        {/* Admin Access */}
        <div className="flex flex-col gap-2">
          <Label size="xsmall" className="txt-compact-small font-medium">
            管理员权限
          </Label>
          <CoolSwitch
            fieldName="is_admin"
            label="管理员"
            description="授予该员工管理员权限"
            checked={formData.is_admin}
            onChange={(checked) =>
              setFormData({ ...formData, is_admin: checked })
            }
          />
        </div>
      </Drawer.Body>

      <Drawer.Footer>
        <div className="flex items-center gap-2">
          <Drawer.Close asChild>
            <Button variant="secondary">取消</Button>
          </Drawer.Close>
          <Button type="submit" disabled={loading}>
            {loading ? "保存中..." : "保存"}
          </Button>
        </div>
      </Drawer.Footer>
    </form>
  )
}
