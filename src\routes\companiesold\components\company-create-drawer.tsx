import { But<PERSON>, Drawer } from "@medusajs/ui";
import { AdminCreateCompany } from "../../../../types";
import { useState } from "react";
import { useCreateCompany } from "../../../hooks/api";
import { useB2BTranslation } from "../../../hooks/use-b2b-translation";
import { CompanyForm } from "./company-form";

export function CompanyCreateDrawer() {
  const { t } = useB2BTranslation();
  const [open, setOpen] = useState(false);

  const { mutateAsync, isPending, error } = useCreateCompany();

  const handleSubmit = async (formData: AdminCreateCompany) => {
    await mutateAsync(formData, {
      onSuccess: () => {
        setOpen(false);
      },
    });
  };

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <Drawer.Trigger asChild>
        <Button variant="secondary" size="small">
          {t("routes.companies.createCompany")}
        </Button>
      </Drawer.Trigger>
      <Drawer.Content>
        <Drawer.Header>
          <Drawer.Title>{t("routes.companies.createCompany")}</Drawer.Title>
        </Drawer.Header>
        <CompanyForm
          handleSubmit={handleSubmit}
          loading={isPending}
          error={error}
        />
      </Drawer.Content>
    </Drawer>
  );
}
