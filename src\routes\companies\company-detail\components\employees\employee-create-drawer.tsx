import { <PERSON><PERSON>, <PERSON>er, toast } from "@medusajs/ui"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { Company, AdminCreateEmployee } from "../../../../../types"
import { useCreateCustomer, useCreateEmployee } from "../../../../../hooks/api"
import { EmployeeCreateForm } from "./employee-create-form"

interface EmployeeCreateDrawerProps {
  company: Company
}

export const EmployeeCreateDrawer = ({ company }: EmployeeCreateDrawerProps) => {
  const { t } = useTranslation()
  const [open, setOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const createCustomer = useCreateCustomer()
  const createEmployee = useCreateEmployee(company.id)

  const handleSubmit = async (data: {
    first_name: string
    last_name: string
    email: string
    phone?: string
    is_admin: boolean
    spending_limit: number
  }) => {
    setIsLoading(true)
    try {
      // First create customer
      const customerResponse = await createCustomer.mutateAsync({
        first_name: data.first_name,
        last_name: data.last_name,
        email: data.email,
        phone: data.phone,
      })

      // Then create employee
      const employeeData: AdminCreateEmployee = {
        customer_id: customerResponse.customer.id,
        is_admin: data.is_admin,
        spending_limit: data.spending_limit,
      }

      await createEmployee.mutateAsync(employeeData)

      toast.success(t("companies.employees.create.success"))
      setOpen(false)
    } catch (error) {
      console.error("Error creating employee:", error)
      toast.error(t("companies.employees.create.error"))
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <Drawer.Trigger asChild>
        <Button variant="secondary" size="small">
          {t("companies.employees.add")}
        </Button>
      </Drawer.Trigger>
      <Drawer.Content>
        <Drawer.Header>
          <Drawer.Title>{t("companies.employees.create.title")}</Drawer.Title>
        </Drawer.Header>
        <EmployeeCreateForm
          company={company}
          handleSubmit={handleSubmit}
          loading={isLoading}
          error={null}
        />
      </Drawer.Content>
    </Drawer>
  )
}
